/**
 * 🧪 AWS Encryption SDK 集成测试
 * 
 * 测试使用 Raw Keyring 的 AWS Encryption SDK 加密解密功能
 */

const { CryptoUtils } = require('./src/index');

async function testAwsEncryptionSdk() {
  console.log('\n🧪 开始 AWS Encryption SDK 集成测试...\n');

  try {
    // 模拟从服务端获取的数据密钥（32字节）
    const mockDataKey = Buffer.alloc(32);
    // 填充一些测试数据
    for (let i = 0; i < 32; i++) {
      mockDataKey[i] = i;
    }
    const dataKeyBase64 = mockDataKey.toString('base64');
    
    const testUserId = 'test-user-123';
    const testData = '这是一段需要加密的敏感数据 🔐';

    console.log('📝 测试数据:', testData);
    console.log('👤 用户ID:', testUserId);
    console.log('🔑 数据密钥长度:', mockDataKey.length, '字节');
    console.log('');

    // 测试 AWS Encryption SDK 加密
    console.log('🔐 测试 AWS Encryption SDK 加密...');
    const encryptedWithAwsSdk = await CryptoUtils.encryptDataWithAwsSdk(
      testData, 
      dataKeyBase64, 
      testUserId
    );
    
    console.log('✅ AWS Encryption SDK 加密成功');
    console.log('📏 加密后长度:', encryptedWithAwsSdk.encrypted.length);
    console.log('🔧 算法:', encryptedWithAwsSdk.algorithm);
    console.log('🔍 加密上下文:', encryptedWithAwsSdk.encryptionContext);
    console.log('');

    // 测试 AWS Encryption SDK 解密
    console.log('🔓 测试 AWS Encryption SDK 解密...');
    const decryptedWithAwsSdk = await CryptoUtils.decryptDataWithAwsSdk(
      encryptedWithAwsSdk, 
      dataKeyBase64
    );
    
    console.log('✅ AWS Encryption SDK 解密成功');
    console.log('📝 解密后数据:', decryptedWithAwsSdk);
    console.log('');

    // 验证数据完整性
    if (decryptedWithAwsSdk === testData) {
      console.log('✅ 数据完整性验证通过');
    } else {
      console.error('❌ 数据完整性验证失败');
      console.error('原始:', testData);
      console.error('解密:', decryptedWithAwsSdk);
      return false;
    }

    console.log('');

    // 测试智能加密方法（默认使用 AWS Encryption SDK）
    console.log('🔐 测试智能加密方法（AWS Encryption SDK）...');
    const encryptedSmart = await CryptoUtils.encryptData(
      testData, 
      dataKeyBase64, 
      testUserId, 
      true // 使用 AWS Encryption SDK
    );
    
    console.log('✅ 智能加密成功');
    console.log('🔧 算法:', encryptedSmart.algorithm);
    console.log('');

    // 测试智能解密方法
    console.log('🔓 测试智能解密方法...');
    const decryptedSmart = await CryptoUtils.decryptData(
      encryptedSmart, 
      dataKeyBase64
    );
    
    console.log('✅ 智能解密成功');
    console.log('📝 解密后数据:', decryptedSmart);
    console.log('');

    // 验证智能方法的数据完整性
    if (decryptedSmart === testData) {
      console.log('✅ 智能方法数据完整性验证通过');
    } else {
      console.error('❌ 智能方法数据完整性验证失败');
      return false;
    }

    console.log('');

    // 测试原生加密兼容性
    console.log('🔐 测试原生加密兼容性...');
    const encryptedNative = await CryptoUtils.encryptData(
      testData, 
      dataKeyBase64, 
      testUserId, 
      false // 使用原生加密
    );
    
    console.log('✅ 原生加密成功');
    console.log('🔧 算法:', encryptedNative.algorithm);
    console.log('');

    // 测试原生解密
    console.log('🔓 测试原生解密...');
    const decryptedNative = await CryptoUtils.decryptData(
      encryptedNative, 
      dataKeyBase64
    );
    
    console.log('✅ 原生解密成功');
    console.log('📝 解密后数据:', decryptedNative);
    console.log('');

    // 验证原生方法的数据完整性
    if (decryptedNative === testData) {
      console.log('✅ 原生方法数据完整性验证通过');
    } else {
      console.error('❌ 原生方法数据完整性验证失败');
      return false;
    }

    console.log('\n🎉 所有测试通过！AWS Encryption SDK 集成成功！\n');
    return true;

  } catch (error) {
    console.error('\n❌ 测试失败:', error);
    console.error('错误详情:', error.stack);
    return false;
  }
}

// 运行测试
if (require.main === module) {
  testAwsEncryptionSdk()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('测试运行失败:', error);
      process.exit(1);
    });
}

module.exports = { testAwsEncryptionSdk };
