const CryptoUtils = require('./utils/crypto');
const { StorageManager } = require('./utils/storage');
const ApiClient = require('./services/apiClient');
const config = require('./config');

/**
 * 🔐 客户端加密库 - 核心类
 * 
 * 安全架构：
 * 1. 🔐 数据不离开客户端：所有敏感数据在客户端加密
 * 2. 🔑 密钥分离：主密钥在KMS，数据密钥临时下发
 * 3. 🛡️ 零信任：服务端无法看到用户数据
 * 4. 🔄 密钥轮换：支持定期更换数据密钥
 * 5. 💾 安全存储：只存储加密后的数据密钥
 */
class ClientSideCrypto {
  constructor(serverUrl) {
    this.apiClient = new ApiClient(serverUrl);
    this.storage = new StorageManager();
    this.dataKey = null;
    this.encryptedDataKey = null;
    this.userId = null;
    this.sessionId = null;
    this.keyFingerprint = null;
  }

  /**
   * 🔑 初始化客户端加密
   * @param {string} userId - 用户ID
   * @param {string} authToken - 认证令牌（可选）
   * @returns {Promise<void>}
   */
  async initialize(userId, authToken = null) {
    try {
      this.userId = userId;
      this.authToken = authToken; // 保存认证令牌
      
      // 🔄 首先尝试恢复会话ID
      const storedSessionId = await this.storage.getSession(userId);
      if (storedSessionId) {
        this.sessionId = storedSessionId;
        console.log(`🔄 恢复会话ID: ${this.sessionId}`);
      } else {
        this.sessionId = CryptoUtils.generateSessionId();
        console.log(`🆕 生成新会话ID: ${this.sessionId}`);
      }
      
      console.log(`🔑 初始化客户端加密 - 用户: ${userId}`);
      
      // 尝试从本地存储恢复加密的数据密钥
      const storedKeyData = await this.storage.getEncryptedDataKey(userId);
      
      if (storedKeyData) {
        console.log('🔄 恢复已存储的数据密钥');
        // 使用存储的完整 EncryptionContext 进行解密
        await this.decryptStoredDataKey(storedKeyData.encryptedDataKey, storedKeyData.encryptionContext);
      } else {
        console.log('🆕 生成新的数据密钥');
        await this.generateNewDataKey();
      }
      
      // 存储会话信息
      await this.storage.storeSession(userId, this.sessionId);
      
      console.log('✅ 客户端加密初始化完成');
      console.log(`🔍 密钥指纹: ${this.keyFingerprint}`);
      
    } catch (error) {
      console.error('❌ 初始化失败:', error);
      throw new Error(`客户端加密初始化失败: ${error.message}`);
    }
  }

  /**
   * 🆕 生成新数据密钥
   * @private
   */
  async generateNewDataKey() {
    try {
      const response = await this.apiClient.generateDataKey(
        this.userId,
        this.sessionId
      );

      this.dataKey = response.plaintextDataKey;
      this.encryptedDataKey = response.encryptedDataKey;
      this.keyFingerprint = CryptoUtils.generateKeyFingerprint(this.dataKey);
      
      // 安全存储加密的数据密钥
      await this.storage.storeEncryptedDataKey(
        this.userId,
        this.encryptedDataKey,
        response.expiresAt,
        response.encryptionContext  // 保存完整的 EncryptionContext
      );
      
      console.log('🔑 新数据密钥已生成并存储');
      
    } catch (error) {
      throw new Error(`生成新数据密钥失败: ${error.message}`);
    }
  }

      /**
   * 🔓 解密存储的数据密钥
   * @param {string} encryptedDataKey - 加密的数据密钥
   * @param {Object} encryptionContext - 原始加密上下文
   * @private
   */
  async decryptStoredDataKey(encryptedDataKey, encryptionContext) {
    try {
      console.log(`📏 准备解密的数据密钥长度: ${encryptedDataKey.length}`);
      console.log(`📋 数据密钥前20字符: ${encryptedDataKey.substring(0, 20)}...`);
      console.log(`� 使用完整 EncryptionContext:`, encryptionContext);
      
      const response = await this.apiClient.decryptDataKey(
        encryptedDataKey,
        encryptionContext  // 传递完整的 EncryptionContext
      );

      this.dataKey = response.plaintextDataKey;
      this.encryptedDataKey = encryptedDataKey;
      this.keyFingerprint = CryptoUtils.generateKeyFingerprint(this.dataKey);
      
      console.log('🔓 已恢复存储的数据密钥');
      console.log(`🔍 密钥指纹: ${this.keyFingerprint}`);
      
    } catch (error) {
      console.error('❌ 解密存储的数据密钥失败:', error);
      console.log('⚠️ 解密存储的数据密钥失败，生成新密钥');
      
      // 清理无效的存储密钥
      await this.storage.removeEncryptedDataKey(this.userId);
      
      // 生成新的数据密钥
      await this.generateNewDataKey();
    }
  }

  /**
   * 🔐 加密敏感数据
   * @param {string} plaintext - 要加密的明文数据
   * @returns {Object} 加密结果
   */
  encryptData(plaintext) {
    if (!this.dataKey) {
      throw new Error('数据密钥未初始化，请先调用 initialize()');
    }

    if (!plaintext || typeof plaintext !== 'string') {
      throw new Error('无效的明文数据');
    }

    console.log('🔐 开始客户端本地加密...');
    console.log('📝 原始数据长度:', plaintext.length);
    console.log('🔍 使用密钥指纹:', this.keyFingerprint);

    const result = CryptoUtils.encryptData(plaintext, this.dataKey);
    
    // 添加元数据
    result.keyFingerprint = this.keyFingerprint;
    result.userId = this.userId;
    result.sessionId = this.sessionId;

    console.log('✅ 客户端加密完成');
    console.log('📏 加密后长度:', result.encrypted.length);
    
    return result;
  }

  /**
   * 🔓 解密敏感数据
   * @param {Object} encryptedData - 加密数据对象
   * @returns {string} 解密后的明文
   */
  decryptData(encryptedData) {
    if (!this.dataKey) {
      throw new Error('数据密钥未初始化，请先调用 initialize()');
    }

    if (!encryptedData || typeof encryptedData !== 'object') {
      throw new Error('无效的加密数据');
    }

    // 验证加密数据完整性
    if (!CryptoUtils.validateEncryptedData(encryptedData)) {
      throw new Error('加密数据验证失败');
    }

    // 验证密钥指纹（可选）
    if (encryptedData.keyFingerprint && 
        encryptedData.keyFingerprint !== this.keyFingerprint) {
      console.warn('⚠️ 密钥指纹不匹配，可能需要密钥轮换');
    }

    console.log('🔓 开始客户端本地解密...');
    console.log('🔍 使用密钥指纹:', this.keyFingerprint);

    const result = CryptoUtils.decryptData(encryptedData, this.dataKey);
    
    console.log('✅ 客户端解密完成');
    console.log('📝 解密后长度:', result.length);
    
    return result;
  }

  /**
   * 🔄 轮换数据密钥
   * @returns {Promise<void>}
   */
  async rotateDataKey() {
    try {
      if (!this.userId || !this.sessionId) {
        throw new Error('未初始化或缺少必要信息');
      }
      
      console.log('🔄 开始轮换数据密钥...');
      
      const oldSessionId = this.sessionId;
      const newSessionId = CryptoUtils.generateSessionId();
      
      const response = await this.apiClient.rotateDataKey(
        this.userId,
        oldSessionId,
        newSessionId
      );

      // 清理旧密钥
      this.cleanup(false);
      
      // 设置新密钥
      this.dataKey = response.plaintextDataKey;
      this.encryptedDataKey = response.encryptedDataKey;
      this.sessionId = newSessionId;
      this.keyFingerprint = CryptoUtils.generateKeyFingerprint(this.dataKey);
      
      // 存储新的加密数据密钥
      await this.storage.storeEncryptedDataKey(
        this.userId,
        this.encryptedDataKey,
        response.expiresAt,
        response.encryptionContext  // 保存完整的 EncryptionContext
      );
      
      // 更新会话信息
      await this.storage.storeSession(this.userId, this.sessionId);
      
      console.log('✅ 数据密钥轮换完成');
      console.log(`🔍 新密钥指纹: ${this.keyFingerprint}`);
      
    } catch (error) {
      console.error('❌ 数据密钥轮换失败:', error);
      throw new Error(`数据密钥轮换失败: ${error.message}`);
    }
  }

  /**
   * 🏥 健康检查
   * @returns {Promise<Object>} 健康状态
   */
  async healthCheck() {
    try {
      const serverHealth = await this.apiClient.healthCheck();
      
      return {
        client: {
          status: 'healthy',
          initialized: !!this.dataKey,
          userId: this.userId,
          keyFingerprint: this.keyFingerprint,
          sessionId: this.sessionId
        },
        server: serverHealth
      };
    } catch (error) {
      return {
        client: {
          status: 'healthy',
          initialized: !!this.dataKey,
          userId: this.userId,
          keyFingerprint: this.keyFingerprint,
          sessionId: this.sessionId
        },
        server: {
          status: 'error',
          error: error.message
        }
      };
    }
  }

  /**
   * 🗑️ 清理敏感数据
   * @param {boolean} clearStorage - 是否清理存储
   */
  cleanup(clearStorage = true) {
    console.log('🗑️ 开始清理敏感数据...');
    
    // 清理内存中的敏感数据
    if (this.dataKey) {
      CryptoUtils.secureCleanup(this.dataKey);
      this.dataKey = null;
    }
    
    this.encryptedDataKey = null;
    this.keyFingerprint = null;
    this.sessionId = null;
    
    // 清理存储（可选）
    if (clearStorage && this.userId) {
      this.storage.cleanup(this.userId).catch(error => {
        console.warn('⚠️ 清理存储时出错:', error);
      });
    }
    
    console.log('✅ 敏感数据清理完成');
  }

  /**
   * 📊 获取状态信息
   * @returns {Object} 状态信息
   */
  getStatus() {
    return {
      initialized: !!this.dataKey,
      userId: this.userId,
      keyFingerprint: this.keyFingerprint,
      sessionId: this.sessionId,
      serverUrl: this.apiClient.serverUrl
    };
  }
}

module.exports = ClientSideCrypto;
