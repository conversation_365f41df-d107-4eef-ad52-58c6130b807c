const axios = require('axios');
const config = require('../config');
const { getAuthToken, clearAuthToken } = require('../utils/storage');

/**
 * 🌐 API客户端 - 使用axios与KMS服务端通信
 * 
 * 安全原则：
 * 1. 使用axios拦截器统一处理认证Token
 * 2. 自动处理401未授权错误
 * 3. 请求超时保护
 */

// 创建axios实例
const apiClient = axios.create({
  baseURL: config.serverUrl,
  timeout: config.timeout.apiRequest,
});

// 请求拦截器：自动附加Authorization头
apiClient.interceptors.request.use(
  async (config) => {
    const token = await getAuthToken();
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`;
      console.log('🔑 请求已附带Token');
    }
    return config;
  },
  (error) => {
    console.error('❌ 请求拦截器错误:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器：处理全局错误，特别是401
apiClient.interceptors.response.use(
  (response) => {
    return response.data;
  },
  async (error) => {
    if (error.response && error.response.status === 401) {
      console.warn('⚠️ 认证失败 (401)，清除本地Token');
      await clearAuthToken();
    }
    const errorMessage = error.response?.data?.error || error.message;
    return Promise.reject(new Error(errorMessage));
  }
);

class ApiClient {
  constructor() {
    // 不再需要构造函数参数，由axios实例管理
  }

  /**
   * 🔑 生成新的数据密钥
   * @param {string} userId - 用户ID
   * @param {string} sessionId - 会话ID
   * @returns {Promise<Object>} 数据密钥响应
   */
  async generateDataKey(userId, sessionId) {
    try {
      console.log(`🔑 请求生成数据密钥 - 用户: ${userId}`);
      
      const response = await apiClient.post('/api/generate-data-key', {
        userId,
        sessionId
      });

      if (!response.success) {
        throw new Error(response.error || '生成数据密钥失败');
      }

      console.log('✅ 数据密钥生成成功');
      return {
        plaintextDataKey: response.plaintextDataKey,
        encryptedDataKey: response.encryptedDataKey,
        keyId: response.keyId,
        expiresAt: response.expiresAt,
        encryptionContext: response.encryptionContext  // 添加加密上下文
      };
      
    } catch (error) {
      console.error('❌ 生成数据密钥请求失败:', error);
      throw error;
    }
  }

  /**
   * 🔓 解密已存储的数据密钥
   * @param {string} encryptedDataKey - 加密的数据密钥
   * @param {Object} encryptionContext - 完整的加密上下文
   * @returns {Promise<Object>} 解密响应
   */
  async decryptDataKey(encryptedDataKey, encryptionContext) {
    try {
      console.log(`🔓 请求解密数据密钥 - 用户: ${encryptionContext.userId}`);
      
      const response = await apiClient.post('/api/decrypt-data-key', {
        encryptedDataKey,
        encryptionContext
      });

      if (!response.success) {
        throw new Error(response.error || '解密数据密钥失败');
      }

      console.log('✅ 数据密钥解密成功');
      return {
        plaintextDataKey: response.plaintextDataKey
      };
      
    } catch (error) {
      console.error('❌ 解密数据密钥请求失败:', error);
      throw error;
    }
  }

  /**
   * 🔄 轮换数据密钥
   * @param {string} userId - 用户ID
   * @param {string} oldSessionId - 旧会话ID
   * @param {string} newSessionId - 新会话ID
   * @returns {Promise<Object>} 新数据密钥响应
   */
  async rotateDataKey(userId, oldSessionId, newSessionId) {
    try {
      console.log(`🔄 请求轮换数据密钥 - 用户: ${userId}`);
      
      const response = await apiClient.post('/api/rotate-data-key', {
        userId,
        oldSessionId,
        newSessionId
      });

      if (!response.success) {
        throw new Error(response.error || '轮换数据密钥失败');
      }

      console.log('✅ 数据密钥轮换成功');
      return {
        plaintextDataKey: response.plaintextDataKey,
        encryptedDataKey: response.encryptedDataKey,
        keyId: response.keyId,
        expiresAt: response.expiresAt,
        encryptionContext: response.encryptionContext  // 添加加密上下文
      };
      
    } catch (error) {
      console.error('❌ 轮换数据密钥请求失败:', error);
      throw error;
    }
  }

  /**
   * 🏥 健康检查
   * @returns {Promise<Object>} 健康状态
   */
  async healthCheck() {
    try {
      const response = await apiClient.get('/api/health');
      return response;
    } catch (error) {
      console.error('❌ 健康检查失败:', error);
      throw error;
    }
  }

  /**
   * 🔧 设置服务器URL
   * @param {string} url - 新的服务器URL
   */
  setServerUrl(url) {
    apiClient.defaults.baseURL = url;
    console.log(`🔧 API服务器地址已更新: ${url}`);
  }

  /**
   * 🕐 设置请求超时
   * @param {number} timeout - 超时时间（毫秒）
   */
  setTimeout(timeout) {
    apiClient.defaults.timeout = timeout;
    console.log(`🕐 API请求超时已更新: ${timeout}ms`);
  }
}

module.exports = ApiClient;
