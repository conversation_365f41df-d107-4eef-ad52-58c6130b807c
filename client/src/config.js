/**
 * 🔧 客户端配置
 */
const config = {
  // 🌐 服务端API地址
  serverUrl: process.env.KMS_SERVER_URL || 'http://localhost:3000',
  
  // 🔐 加密配置
  encryption: {
    algorithm: 'aes-256-gcm',
    keyLength: 32, // 256位
    ivLength: 16,  // 128位
    tagLength: 16  // 128位
  },
  
  // 🕐 超时配置
  timeout: {
    apiRequest: 10000,    // API请求超时 10秒
    keyExpiry: 24 * 60 * 60 * 1000  // 密钥过期时间 24小时
  },
  
  // 💾 存储配置
  storage: {
    keyPrefix: 'kms_encrypted_key_',
    sessionPrefix: 'kms_session_',
    maxRetries: 3
  },
  
  // 🔒 安全配置
  security: {
    clearMemoryOnCleanup: true,
    validateKeyExpiry: true,
    enforceHttps: process.env.NODE_ENV === 'production'
  },
  
  // 📊 日志配置
  logging: {
    enabled: process.env.NODE_ENV !== 'production',
    level: 'info' // debug, info, warn, error
  }
};

// 🌍 环境特定配置
if (typeof window !== 'undefined') {
  // 浏览器环境
  config.storage.provider = 'localStorage';
} else {
  // Node.js环境
  config.storage.provider = 'memory';
}

module.exports = config;
