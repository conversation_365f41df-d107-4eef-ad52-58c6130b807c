const config = require('../config');
const TempFileStorageProvider = require('./tempFileStorage');

/**
 * 💾 客户端存储管理器
 * 
 * 安全原则：
 * 1. 只存储加密后的数据密钥
 * 2. 支持多种存储后端
 * 3. 自动过期清理
 * 4. 安全删除
 * 5. 安全存储认证Token
 */
class StorageManager {
  constructor() {
    this.provider = this.initializeProvider();
  }

  /**
   * 初始化存储提供者
   */
  initializeProvider() {
    if (config.storage.provider === 'localStorage' && typeof window !== 'undefined') {
      return new LocalStorageProvider();
    } else {
      // 使用文件存储替代内存存储，实现持久化
      return new TempFileStorageProvider();
    }
  }

  /**
   * 🔑 存储认证Token
   * @param {string} token - JWT Token
   */
  async storeAuthToken(token) {
    const key = `${config.storage.keyPrefix}auth_token`;
    await this.provider.setItem(key, token);
    console.log('🔑 认证Token已存储');
  }

  /**
   * 🔑 获取认证Token
   * @returns {string|null} JWT Token
   */
  async getAuthToken() {
    const key = `${config.storage.keyPrefix}auth_token`;
    return await this.provider.getItem(key);
  }

  /**
   * 🗑️ 清除认证Token
   */
  async clearAuthToken() {
    const key = `${config.storage.keyPrefix}auth_token`;
    await this.provider.removeItem(key);
    console.log('🗑️ 认证Token已清除');
  }

  /**
   * 🔒 存储加密的数据密钥
   * @param {string} userId - 用户ID
   * @param {string} encryptedDataKey - 加密的数据密钥
   * @param {number} expiresAt - 过期时间戳
   * @param {Object} encryptionContext - 完整的加密上下文
   */
  async storeEncryptedDataKey(userId, encryptedDataKey, expiresAt, encryptionContext) {
    const key = `${config.storage.keyPrefix}${userId}`;
    const data = {
      encryptedDataKey,
      expiresAt,
      encryptionContext,  // 保存完整的加密上下文
      createdAt: Date.now()
    };
    
    await this.provider.setItem(key, JSON.stringify(data));
    console.log(`💾 已存储用户 ${userId} 的加密数据密钥（含完整上下文）`);
  }

  /**
   * 🔓 获取加密的数据密钥
   * @param {string} userId - 用户ID
   * @returns {Object|null} 包含加密数据密钥和会话ID的对象
   */
  async getEncryptedDataKey(userId) {
    const key = `${config.storage.keyPrefix}${userId}`;
    const dataStr = await this.provider.getItem(key);
    
    if (!dataStr) {
      return null;
    }
    
    try {
      const data = JSON.parse(dataStr);
      
      // 检查是否过期
      if (config.security.validateKeyExpiry && Date.now() > data.expiresAt) {
        console.log(`⏰ 用户 ${userId} 的数据密钥已过期，自动清理`);
        await this.removeEncryptedDataKey(userId);
        return null;
      }
      
      console.log(`🔑 获取用户 ${userId} 的加密数据密钥（含完整上下文）`);
      return {
        encryptedDataKey: data.encryptedDataKey,
        encryptionContext: data.encryptionContext,
        expiresAt: data.expiresAt,
        createdAt: data.createdAt
      };
      
    } catch (error) {
      console.error('❌ 解析存储数据失败:', error);
      await this.removeEncryptedDataKey(userId);
      return null;
    }
  }

  /**
   * 🗑️ 删除加密的数据密钥
   * @param {string} userId - 用户ID
   */
  async removeEncryptedDataKey(userId) {
    const key = `${config.storage.keyPrefix}${userId}`;
    await this.provider.removeItem(key);
    console.log(`🗑️ 已删除用户 ${userId} 的加密数据密钥`);
  }

  /**
   * 💾 存储会话信息
   * @param {string} userId - 用户ID
   * @param {string} sessionId - 会话ID
   */
  async storeSession(userId, sessionId) {
    const key = `${config.storage.sessionPrefix}${userId}`;
    const data = {
      sessionId,
      createdAt: Date.now()
    };
    
    await this.provider.setItem(key, JSON.stringify(data));
    console.log(`💾 已存储用户 ${userId} 的会话信息`);
  }

  /**
   * 🔍 获取会话信息
   * @param {string} userId - 用户ID
   * @returns {string|null} 会话ID
   */
  async getSession(userId) {
    const key = `${config.storage.sessionPrefix}${userId}`;
    const dataStr = await this.provider.getItem(key);
    
    if (!dataStr) {
      return null;
    }
    
    try {
      const data = JSON.parse(dataStr);
      return data.sessionId;
    } catch (error) {
      console.error('❌ 解析会话数据失败:', error);
      return null;
    }
  }

  /**
   * 🧹 清理所有存储数据
   * @param {string} userId - 用户ID
   */
  async cleanup(userId) {
    await this.removeEncryptedDataKey(userId);
    
    const sessionKey = `${config.storage.sessionPrefix}${userId}`;
    await this.provider.removeItem(sessionKey);
    
    console.log(`🧹 已清理用户 ${userId} 的所有存储数据`);
  }

  /**
   * 🔍 列出所有存储的密钥
   * @returns {Array} 密钥列表
   */
  async listStoredKeys() {
    return await this.provider.listKeys(config.storage.keyPrefix);
  }
}

/**
 * 🌐 LocalStorage提供者（浏览器环境）
 */
class LocalStorageProvider {
  async setItem(key, value) {
    localStorage.setItem(key, value);
  }

  async getItem(key) {
    return localStorage.getItem(key);
  }

  async removeItem(key) {
    localStorage.removeItem(key);
  }

  async listKeys(prefix) {
    const keys = [];
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith(prefix)) {
        keys.push(key);
      }
    }
    return keys;
  }
}

/**
 * 💾 内存存储提供者（Node.js环境）
 */
class MemoryStorageProvider {
  constructor() {
    this.storage = new Map();
  }

  async setItem(key, value) {
    this.storage.set(key, value);
  }

  async getItem(key) {
    return this.storage.get(key) || null;
  }

  async removeItem(key) {
    this.storage.delete(key);
  }

  async listKeys(prefix) {
    const keys = [];
    for (const key of this.storage.keys()) {
      if (key.startsWith(prefix)) {
        keys.push(key);
      }
    }
    return keys;
  }
}

// 导出便捷函数供 apiClient 使用
const storage = new StorageManager();

// 认证Token相关的便捷函数
const storeAuthToken = (token) => storage.storeAuthToken(token);
const getAuthToken = () => storage.getAuthToken();
const clearAuthToken = () => storage.clearAuthToken();

// 数据密钥相关的便捷函数
const storeEncryptedDataKey = (userId, encryptedDataKey, expiresAt, encryptionContext) => 
  storage.storeEncryptedDataKey(userId, encryptedDataKey, expiresAt, encryptionContext);
const getEncryptedDataKey = (userId) => storage.getEncryptedDataKey(userId);
const clearEncryptedDataKey = (userId) => storage.clearEncryptedDataKey(userId);

module.exports = {
  StorageManager,
  // 认证相关
  storeAuthToken,
  getAuthToken,
  clearAuthToken,
  // 数据密钥相关
  storeEncryptedDataKey,
  getEncryptedDataKey,
  clearEncryptedDataKey,
};
