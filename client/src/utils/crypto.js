const crypto = require("crypto");
const {
  buildClient,
  CommitmentPolicy,
  RawAesKeyringNode,
  RawAesWrappingSuiteIdentifier,
} = require("@aws-crypto/client-node");
const config = require("../config");

/**
 * 🔐 客户端加密工具类 - 使用 AWS Encryption SDK
 *
 * 安全原则：
 * 1. 所有加密操作在客户端本地进行
 * 2. 敏感数据永远不发送到服务端
 * 3. 完全使用 AWS Encryption SDK 官方实现
 * 4. 使用 Raw Keyring，无需客户端访问 KMS
 * 5. 获得 AWS 官方背书和支持
 * 6. 密钥在内存中及时清理
 */
class CryptoUtils {
  /**
   * 🔧 获取 AWS Encryption SDK 客户端
   * @returns {Object} AWS Encryption SDK 客户端
   * @private
   */
  static _getAwsClient() {
    if (!this._awsClient) {
      this._awsClient = buildClient(
        CommitmentPolicy.REQUIRE_ENCRYPT_REQUIRE_DECRYPT
      );
    }
    return this._awsClient;
  }

  /**
   * 🔐 使用 AWS Encryption SDK 加密数据（使用 Raw Keyring）
   * @param {string} plaintext - 明文数据
   * @param {string} dataKey - Base64编码的数据密钥
   * @param {string} userId - 用户ID（用于加密上下文）
   * @returns {Promise<Object>} 加密结果
   */
  static async encryptDataWithAwsSdk(plaintext, dataKey, userId) {
    try {
      console.log("🔐 开始 AWS Encryption SDK 客户端本地加密...");
      console.log("📝 原始数据长度:", plaintext.length);
      console.log("👤 用户ID:", userId);

      // 创建包含用户ID的数据包
      const dataPacket = {
        userId: userId,
        timestamp: Date.now(),
        data: plaintext,
      };
      const dataToEncrypt = JSON.stringify(dataPacket);

      // 解码数据密钥
      const keyBuffer = Buffer.from(dataKey, "base64");

      // 确保使用32字节密钥，并创建 Uint8Array（AWS Encryption SDK 要求）
      let actualKey;
      if (keyBuffer.length === 32) {
        // 创建一个 Uint8Array
        actualKey = new Uint8Array(keyBuffer);
      } else if (keyBuffer.length === 48) {
        // 兼容旧架构：提取32字节AES密钥
        actualKey = new Uint8Array(keyBuffer.subarray(16, 48));
      } else {
        // 标准密钥，确保是32字节
        if (keyBuffer.length >= 32) {
          actualKey = new Uint8Array(keyBuffer.subarray(0, 32));
        } else {
          actualKey = new Uint8Array(32);
          actualKey.set(new Uint8Array(keyBuffer));
        }
      }

      // 创建 Raw AES Keyring（不需要访问 KMS）
      const keyName = `user-${userId}-key`;
      const keyNamespace = "client-side-encryption";
      const wrappingSuite =
        RawAesWrappingSuiteIdentifier.AES256_GCM_IV12_TAG16_NO_PADDING;

      const keyring = new RawAesKeyringNode({
        keyName,
        keyNamespace,
        unencryptedMasterKey: actualKey,
        wrappingSuite,
      });

      // 设置加密上下文
      const encryptionContext = {
        userId: userId,
        purpose: "client-side-encryption",
        timestamp: Date.now().toString(),
      };

      // 获取 AWS Encryption SDK 客户端
      const client = this._getAwsClient();

      // 使用 AWS Encryption SDK 进行加密
      const { result: encryptedBuffer } = await client.encrypt(
        keyring,
        dataToEncrypt,
        {
          encryptionContext,
        }
      );

      const result = {
        encrypted: Buffer.from(encryptedBuffer).toString("base64"),
        algorithm: "aws-encryption-sdk",
        timestamp: Date.now(),
        encryptionContext: encryptionContext,
        keyName: keyName,
        keyNamespace: keyNamespace,
        // 保持与现有格式的兼容性
        iv: null,
        authTag: null,
      };

      console.log("✅ AWS Encryption SDK 客户端加密完成");
      console.log("📏 加密后长度:", result.encrypted.length);

      // 清理内存中的密钥
      actualKey.fill(0);
      keyBuffer.fill(0);

      return result;
    } catch (error) {
      console.error("❌ AWS Encryption SDK 客户端加密失败:", error);
      throw new Error(`AWS Encryption SDK 加密失败: ${error.message}`);
    }
  }

  /**
   * 🔓 使用 AWS Encryption SDK 解密数据（使用 Raw Keyring）
   * @param {Object} encryptedData - 加密数据对象
   * @param {string} dataKey - Base64编码的数据密钥
   * @returns {Promise<string>} 解密后的明文
   */
  static async decryptDataWithAwsSdk(encryptedData, dataKey) {
    try {
      console.log("🔓 开始 AWS Encryption SDK 客户端本地解密...");

      // 解码数据密钥
      const keyBuffer = Buffer.from(dataKey, "base64");

      // 确保使用32字节密钥，并创建 Uint8Array（AWS Encryption SDK 要求）
      let actualKey;
      if (keyBuffer.length === 32) {
        // 创建一个 Uint8Array
        actualKey = new Uint8Array(keyBuffer);
      } else if (keyBuffer.length === 48) {
        // 兼容旧架构：提取32字节AES密钥
        actualKey = new Uint8Array(keyBuffer.subarray(16, 48));
      } else {
        // 标准密钥，确保是32字节
        if (keyBuffer.length >= 32) {
          actualKey = new Uint8Array(keyBuffer.subarray(0, 32));
        } else {
          actualKey = new Uint8Array(32);
          actualKey.set(new Uint8Array(keyBuffer));
        }
      }

      // 创建 Raw AES Keyring（使用存储的 keyName 和 keyNamespace）
      const keyName =
        encryptedData.keyName ||
        `user-${encryptedData.encryptionContext?.userId}-key`;
      const keyNamespace =
        encryptedData.keyNamespace || "client-side-encryption";
      const wrappingSuite =
        RawAesWrappingSuiteIdentifier.AES256_GCM_IV12_TAG16_NO_PADDING;

      const keyring = new RawAesKeyringNode({
        keyName,
        keyNamespace,
        unencryptedMasterKey: actualKey,
        wrappingSuite,
      });

      // 获取 AWS Encryption SDK 客户端
      const client = this._getAwsClient();

      // 从 base64 转换回二进制数据
      const ciphertext = Buffer.from(encryptedData.encrypted, "base64");

      // 使用 AWS Encryption SDK 进行解密
      const { plaintext, messageHeader } = await client.decrypt(
        keyring,
        ciphertext
      );

      // 解析解密后的数据包
      const decryptedText = plaintext.toString("utf8");
      const dataPacket = JSON.parse(decryptedText);

      console.log("✅ AWS Encryption SDK 客户端解密完成");
      console.log("📝 解密后数据长度:", dataPacket.data.length);
      console.log("👤 数据包中的用户ID:", dataPacket.userId);
      console.log(
        "🕐 数据包时间戳:",
        new Date(dataPacket.timestamp).toISOString()
      );

      // 验证加密上下文（如果存在）
      if (encryptedData.encryptionContext && messageHeader.encryptionContext) {
        console.log("🔍 验证加密上下文...");
        const expectedUserId = encryptedData.encryptionContext.userId;
        const actualUserId = messageHeader.encryptionContext.userId;

        if (expectedUserId && actualUserId !== expectedUserId) {
          throw new Error(
            `用户ID不匹配: 期望 ${expectedUserId}, 实际 ${actualUserId}`
          );
        }
      }

      // 清理内存中的密钥
      actualKey.fill(0);
      keyBuffer.fill(0);

      return dataPacket.data;
    } catch (error) {
      console.error("❌ AWS Encryption SDK 客户端解密失败:", error);
      throw new Error(`AWS Encryption SDK 解密失败: ${error.message}`);
    }
  }

  /**
   * 🔐 使用 AWS Encryption SDK 加密数据
   * @param {string} plaintext - 明文数据
   * @param {string} dataKey - Base64编码的数据密钥
   * @param {string} userId - 用户ID（用于加密上下文）
   * @returns {Promise<Object>} 加密结果
   */
  static async encryptData(plaintext, dataKey, userId) {
    return await this.encryptDataWithAwsSdk(plaintext, dataKey, userId);
  }

  /**
   * 🔓 使用 AWS Encryption SDK 解密数据
   * @param {Object} encryptedData - 加密数据对象
   * @param {string} dataKey - Base64编码的数据密钥
   * @returns {Promise<string>} 解密后的明文
   */
  static async decryptData(encryptedData, dataKey) {
    return await this.decryptDataWithAwsSdk(encryptedData, dataKey);
  }

  /**
   * 🔍 验证 AWS Encryption SDK 加密数据完整性
   * @param {Object} encryptedData - 加密数据对象
   * @returns {boolean} 验证结果
   */
  static validateEncryptedData(encryptedData) {
    // 基本字段检查
    const requiredFields = [
      "encrypted",
      "algorithm",
      "timestamp",
      "encryptionContext",
    ];

    for (const field of requiredFields) {
      if (!encryptedData[field]) {
        console.warn(`❌ 缺少必需字段: ${field}`);
        return false;
      }
    }

    // 验证算法类型
    if (encryptedData.algorithm !== "aws-encryption-sdk") {
      console.warn("❌ 不支持的加密算法:", encryptedData.algorithm);
      return false;
    }

    // 验证时间戳（防止重放攻击）
    const age = Date.now() - encryptedData.timestamp;
    if (age > config.timeout.keyExpiry) {
      console.warn("❌ 加密数据已过期");
      return false;
    }

    return true;
  }

  /**
   * 🗑️ 安全清理内存
   * @param {Buffer|string} data - 要清理的数据
   */
  static secureCleanup(data) {
    if (Buffer.isBuffer(data)) {
      data.fill(0);
    } else if (typeof data === "string") {
      // 字符串无法直接清理，但可以重新赋值
      data = null;
    }
  }

  /**
   * 🔐 生成密钥指纹
   * @param {string} dataKey - 数据密钥
   * @returns {string} 密钥指纹
   */
  static generateKeyFingerprint(dataKey) {
    const hash = crypto.createHash("sha256");
    hash.update(dataKey);
    return hash.digest("hex").substring(0, 16);
  }
}

module.exports = CryptoUtils;
