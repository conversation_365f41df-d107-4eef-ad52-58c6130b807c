const crypto = require('crypto');
const config = require('../config');

/**
 * 🔐 客户端加密工具类
 * 
 * 安全原则：
 * 1. 所有加密操作在客户端本地进行
 * 2. 敏感数据永远不发送到服务端
 * 3. 使用强加密算法（AES-256-GCM）
 * 4. 密钥在内存中及时清理
 */
class CryptoUtils {
  
  /**
   * 🔐 加密数据
   * @param {string} plaintext - 明文数据
   * @param {string} dataKey - Base64编码的数据密钥（可能包含嵌入的用户ID）
   * @param {string} userId - 用户ID（用于数据级安全验证）
   * @returns {Object} 加密结果
   */
  static encryptData(plaintext, dataKey, userId) {
    try {
      console.log('🔐 开始客户端本地加密...');
      console.log('📝 原始数据长度:', plaintext.length);
      console.log('👤 嵌入用户ID:', userId);

      // 创建包含用户ID的数据包
      const dataPacket = {
        userId: userId,
        timestamp: Date.now(),
        data: plaintext
      };
      const dataToEncrypt = JSON.stringify(dataPacket);

      // 解码数据密钥
      const keyBuffer = Buffer.from(dataKey, 'base64');
      
      // 🔍 直接使用密钥进行加密
      let actualKey;
      if (keyBuffer.length === 32) {
        // 新架构：直接使用完整的32字节作为AES-256密钥
        // 用户ID已嵌入在密钥中，用户身份验证在服务端进行
        actualKey = keyBuffer;
        console.log('🔍 使用32字节密钥直接作为AES-256密钥');
        console.log('📏 密钥长度:', actualKey.length);
      } else if (keyBuffer.length === 48) {
        // 兼容旧架构：提取32字节AES密钥
        actualKey = keyBuffer.slice(16, 48);
        console.log('🔍 兼容旧架构：提取32字节AES密钥');
        console.log('📏 原始密钥长度:', keyBuffer.length, '-> 提取后:', actualKey.length);
      } else {
        // 标准密钥
        actualKey = keyBuffer;
        console.log('🔍 使用标准密钥，长度:', actualKey.length);
      }
      
      // 生成随机IV
      const iv = crypto.randomBytes(config.encryption.ivLength);
      
      // 创建加密器
      const cipher = crypto.createCipheriv(config.encryption.algorithm, actualKey, iv);
      
      // 加密包含用户ID的数据包
      let encrypted = cipher.update(dataToEncrypt, 'utf8', 'hex');
      encrypted += cipher.final('hex');
      
      // 获取认证标签
      const authTag = cipher.getAuthTag();
      
      const result = {
        encrypted,
        iv: iv.toString('hex'),
        authTag: authTag.toString('hex'),
        algorithm: config.encryption.algorithm,
        timestamp: Date.now()
      };

      console.log('✅ 客户端加密完成');
      console.log('📏 加密后长度:', result.encrypted.length);
      
      // 清理内存中的密钥
      actualKey.fill(0);
      keyBuffer.fill(0);
      
      return result;
      
    } catch (error) {
      console.error('❌ 客户端加密失败:', error);
      throw new Error(`加密失败: ${error.message}`);
    }
  }

  /**
   * 🔓 解密数据
   * @param {Object} encryptedData - 加密数据对象
   * @param {string} dataKey - Base64编码的数据密钥（可能包含嵌入的用户ID）
   * @returns {string} 解密后的明文
   */
  static decryptData(encryptedData, dataKey) {
    try {
      console.log('🔓 开始客户端本地解密...');

      // 解码数据密钥
      const keyBuffer = Buffer.from(dataKey, 'base64');
      
      // 🔍 直接使用密钥进行解密
      let actualKey;
      if (keyBuffer.length === 32) {
        // 新架构：直接使用完整的32字节作为AES-256密钥
        // 用户ID已嵌入在密钥中，用户身份验证在服务端进行
        actualKey = keyBuffer;
        console.log('🔍 使用32字节密钥直接作为AES-256密钥');
        console.log('📏 密钥长度:', actualKey.length);
      } else if (keyBuffer.length === 48) {
        // 兼容旧架构：提取32字节AES密钥
        actualKey = keyBuffer.slice(16, 48);
        console.log('🔍 兼容旧架构：提取32字节AES密钥');
        console.log('📏 原始密钥长度:', keyBuffer.length, '-> 提取后:', actualKey.length);
      } else {
        // 标准密钥
        actualKey = keyBuffer;
        console.log('🔍 使用标准密钥，长度:', actualKey.length);
      }
      
      // 创建解密器
      const decipher = crypto.createDecipheriv(
        encryptedData.algorithm || config.encryption.algorithm,
        actualKey,
        Buffer.from(encryptedData.iv, 'hex')
      );
      
      // 设置认证标签
      decipher.setAuthTag(Buffer.from(encryptedData.authTag, 'hex'));
      
      // 解密数据
      let decrypted = decipher.update(encryptedData.encrypted, 'hex', 'utf8');
      decrypted += decipher.final('utf8');
      
      // 解析JSON数据包并提取原始数据
      try {
        const dataPacket = JSON.parse(decrypted);
        const originalData = dataPacket.data;
        
        console.log('✅ 客户端解密完成');
        console.log('📝 解密后长度:', originalData.length);
        
        // 清理内存中的密钥
        actualKey.fill(0);
        keyBuffer.fill(0);
        
        return originalData;
      } catch (parseError) {
        // 如果不是JSON格式，可能是旧版本加密的数据，直接返回
        console.log('⚠️ 解密数据不是JSON格式，可能是旧版本数据');
        console.log('✅ 客户端解密完成');
        console.log('📝 解密后长度:', decrypted.length);
        
        // 清理内存中的密钥
        actualKey.fill(0);
        keyBuffer.fill(0);
        
        return decrypted;
      }
      
    } catch (error) {
      console.error('❌ 客户端解密失败:', error);
      throw new Error(`解密失败: ${error.message}`);
    }
  }

  /**
   * 🎲 生成会话ID
   * @returns {string} 随机会话ID
   */
  static generateSessionId() {
    return crypto.randomBytes(16).toString('hex');
  }

  /**
   * 🔍 验证加密数据完整性
   * @param {Object} encryptedData - 加密数据对象
   * @returns {boolean} 验证结果
   */
  static validateEncryptedData(encryptedData) {
    const requiredFields = ['encrypted', 'iv', 'authTag', 'algorithm', 'timestamp'];
    
    for (const field of requiredFields) {
      if (!encryptedData[field]) {
        console.warn(`❌ 缺少必需字段: ${field}`);
        return false;
      }
    }
    
    // 验证时间戳（防止重放攻击）
    const age = Date.now() - encryptedData.timestamp;
    if (age > config.timeout.keyExpiry) {
      console.warn('❌ 加密数据已过期');
      return false;
    }
    
    return true;
  }

  /**
   * 🗑️ 安全清理内存
   * @param {Buffer|string} data - 要清理的数据
   */
  static secureCleanup(data) {
    if (Buffer.isBuffer(data)) {
      data.fill(0);
    } else if (typeof data === 'string') {
      // 字符串无法直接清理，但可以重新赋值
      data = null;
    }
  }

  /**
   * 🔐 生成密钥指纹
   * @param {string} dataKey - 数据密钥
   * @returns {string} 密钥指纹
   */
  static generateKeyFingerprint(dataKey) {
    const hash = crypto.createHash('sha256');
    hash.update(dataKey);
    return hash.digest('hex').substring(0, 16);
  }
}

module.exports = CryptoUtils;
