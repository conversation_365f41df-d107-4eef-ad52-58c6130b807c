const { buildClient, CommitmentPolicy, KmsKeyringNode } = require('@aws-crypto/client-node');
const config = require('../config');

/**
 * 🔐 AWS Encryption SDK 加密工具类
 * 
 * 安全原则：
 * 1. 使用 AWS Encryption SDK 进行信封加密
 * 2. 所有加密操作在客户端本地进行
 * 3. 敏感数据永远不发送到服务端
 * 4. 使用 AWS 官方推荐的加密实现
 * 5. 密钥在内存中及时清理
 */
class AwsCryptoUtils {
  
  constructor() {
    // 创建 AWS Encryption SDK 客户端
    this.client = buildClient(CommitmentPolicy.REQUIRE_ENCRYPT_REQUIRE_DECRYPT);
  }

  /**
   * 🔐 使用 AWS Encryption SDK 加密数据
   * @param {string} plaintext - 明文数据
   * @param {string} dataKey - Base64编码的数据密钥
   * @param {string} userId - 用户ID（用于加密上下文）
   * @param {string} kmsKeyArn - KMS密钥ARN（用于创建keyring）
   * @returns {Object} 加密结果
   */
  async encryptData(plaintext, dataKey, userId, kmsKeyArn) {
    try {
      console.log('🔐 开始 AWS Encryption SDK 客户端本地加密...');
      console.log('📝 原始数据长度:', plaintext.length);
      console.log('👤 用户ID:', userId);

      // 创建包含用户ID的数据包
      const dataPacket = {
        userId: userId,
        timestamp: Date.now(),
        data: plaintext
      };
      const dataToEncrypt = JSON.stringify(dataPacket);

      // 创建 KMS Keyring
      const keyring = new KmsKeyringNode({
        keyIds: [kmsKeyArn],
        generatorKeyId: kmsKeyArn
      });

      // 设置加密上下文
      const encryptionContext = {
        userId: userId,
        purpose: 'client-side-encryption',
        timestamp: Date.now().toString()
      };

      // 使用 AWS Encryption SDK 进行加密
      const { result } = await this.client.encrypt(keyring, dataToEncrypt, {
        encryptionContext
      });

      // 将结果转换为与现有API兼容的格式
      const encryptedResult = {
        encrypted: Buffer.from(result).toString('base64'),
        algorithm: 'aws-encryption-sdk',
        timestamp: Date.now(),
        encryptionContext: encryptionContext,
        // 保持与现有格式的兼容性
        iv: null, // AWS Encryption SDK 内部管理
        authTag: null // AWS Encryption SDK 内部管理
      };

      console.log('✅ AWS Encryption SDK 客户端加密完成');
      console.log('📏 加密后长度:', encryptedResult.encrypted.length);
      
      return encryptedResult;
      
    } catch (error) {
      console.error('❌ AWS Encryption SDK 客户端加密失败:', error);
      throw new Error(`AWS Encryption SDK 加密失败: ${error.message}`);
    }
  }

  /**
   * 🔓 使用 AWS Encryption SDK 解密数据
   * @param {Object} encryptedData - 加密数据对象
   * @param {string} dataKey - Base64编码的数据密钥
   * @param {string} kmsKeyArn - KMS密钥ARN（用于创建keyring）
   * @returns {string} 解密后的明文
   */
  async decryptData(encryptedData, dataKey, kmsKeyArn) {
    try {
      console.log('🔓 开始 AWS Encryption SDK 客户端本地解密...');

      // 创建 KMS Keyring
      const keyring = new KmsKeyringNode({
        keyIds: [kmsKeyArn],
        generatorKeyId: kmsKeyArn
      });

      // 从 base64 转换回二进制数据
      const ciphertext = Buffer.from(encryptedData.encrypted, 'base64');

      // 使用 AWS Encryption SDK 进行解密
      const { plaintext, messageHeader } = await this.client.decrypt(keyring, ciphertext);

      // 解析解密后的数据包
      const decryptedText = plaintext.toString('utf8');
      const dataPacket = JSON.parse(decryptedText);

      console.log('✅ AWS Encryption SDK 客户端解密完成');
      console.log('📝 解密后数据长度:', dataPacket.data.length);
      console.log('👤 数据包中的用户ID:', dataPacket.userId);
      console.log('🕐 数据包时间戳:', new Date(dataPacket.timestamp).toISOString());

      // 验证加密上下文（如果存在）
      if (encryptedData.encryptionContext && messageHeader.encryptionContext) {
        console.log('🔍 验证加密上下文...');
        const expectedUserId = encryptedData.encryptionContext.userId;
        const actualUserId = messageHeader.encryptionContext.userId;
        
        if (expectedUserId && actualUserId !== expectedUserId) {
          throw new Error(`用户ID不匹配: 期望 ${expectedUserId}, 实际 ${actualUserId}`);
        }
      }

      return dataPacket.data;
      
    } catch (error) {
      console.error('❌ AWS Encryption SDK 客户端解密失败:', error);
      throw new Error(`AWS Encryption SDK 解密失败: ${error.message}`);
    }
  }

  /**
   * 🔍 验证加密数据完整性（AWS Encryption SDK 版本）
   * @param {Object} encryptedData - 加密数据对象
   * @returns {boolean} 验证结果
   */
  static validateEncryptedData(encryptedData) {
    const requiredFields = ['encrypted', 'algorithm', 'timestamp'];
    
    for (const field of requiredFields) {
      if (!encryptedData[field]) {
        console.warn(`❌ 缺少必需字段: ${field}`);
        return false;
      }
    }
    
    // 验证算法类型
    if (encryptedData.algorithm !== 'aws-encryption-sdk') {
      console.warn('❌ 不支持的加密算法:', encryptedData.algorithm);
      return false;
    }
    
    // 验证时间戳（防止重放攻击）
    const age = Date.now() - encryptedData.timestamp;
    if (age > config.timeout.keyExpiry) {
      console.warn('❌ 加密数据已过期');
      return false;
    }
    
    return true;
  }

  /**
   * 🎲 生成会话ID
   * @returns {string} 随机会话ID
   */
  static generateSessionId() {
    const crypto = require('crypto');
    return crypto.randomBytes(16).toString('hex');
  }

  /**
   * 🔍 生成密钥指纹
   * @param {string} dataKey - Base64编码的数据密钥
   * @returns {string} 密钥指纹
   */
  static generateKeyFingerprint(dataKey) {
    const crypto = require('crypto');
    return crypto.createHash('sha256').update(dataKey).digest('hex').substring(0, 16);
  }

  /**
   * 🔄 检查是否支持 AWS Encryption SDK
   * @returns {boolean} 支持状态
   */
  static isAwsEncryptionSdkSupported() {
    try {
      require('@aws-crypto/client-node');
      return true;
    } catch (error) {
      console.warn('⚠️ AWS Encryption SDK 不可用，将回退到原生加密');
      return false;
    }
  }
}

module.exports = AwsCryptoUtils;
