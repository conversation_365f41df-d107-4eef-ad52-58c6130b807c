const ClientSideCrypto = require("./clientSideCrypto");
const axios = require("axios");
const { storeAuthToken, getAuthToken } = require("./utils/storage");

/**
 * 🔐 客户端加密演示
 *
 * 展示完整的客户端加密流程：
 * 1. 登录获取JWT令牌
 * 2. 初始化客户端加密
 * 3. 加密敏感数据
 * 4. 解密数据验证
 * 5. 密钥轮换演示
 * 6. 清理资源
 */

/**
 * 🔑 演示登录功能
 */
async function loginDemo() {
  console.log("📋 步骤1：用户登录");
  console.log("----------------------------------------");

  try {
    const response = await axios.post("http://localhost:3000/api/login", {
      username: "user123",
      password: "password123",
    });

    if (response.data.success) {
      console.log("✅ 登录成功");
      console.log(`   用户: ${response.data.user.userId}`);
      console.log(`   角色: ${response.data.user.role}`);

      // 存储Token到本地
      await storeAuthToken(response.data.token);
      console.log("💾 Token已存储到本地");

      return response.data.token;
    } else {
      throw new Error("登录失败");
    }
  } catch (error) {
    console.error("❌ 登录失败:", error.response?.data?.error || error.message);
    throw error;
  }
}

async function clientEncryptionDemo() {
  console.log("\n🔐 KMS客户端加密演示");
  console.log("=====================================");
  console.log("🛡️ 安全特性：");
  console.log("  • 用户身份认证");
  console.log("  • 数据不离开客户端");
  console.log("  • 密钥分离架构");
  console.log("  • 零信任安全");
  console.log("  • 密钥轮换支持");
  console.log("  • 安全存储");
  console.log("=====================================\n");

  // 创建客户端实例
  let crypto = new ClientSideCrypto("http://localhost:3000");

  try {
    // 🔑 步骤1：用户登录
    const token = await loginDemo();
    console.log("");

    // 🔑 步骤2：初始化客户端加密
    console.log("📋 步骤2：初始化客户端加密");
    console.log("----------------------------------------");
    await crypto.initialize("user123");

    // 显示初始状态
    const initialStatus = crypto.getStatus();
    console.log("✅ 初始化完成");
    console.log(`   用户ID: ${initialStatus.userId}`);
    console.log(`   密钥指纹: ${initialStatus.keyFingerprint}`);
    console.log(`   会话ID: ${initialStatus.sessionId}`);
    console.log("");

    // 🔐 步骤3：加密敏感数据
    console.log("📋 步骤3：加密敏感数据");
    console.log("----------------------------------------");

    const testCases = [
      "这是用户的敏感信息，银行卡号：1234-5678-9012-3456",
      "Personal data: John Doe, SSN: ***********",
      JSON.stringify({
        user: "user123",
        balance: 50000.0,
        transactions: ["tx1", "tx2", "tx3"],
        timestamp: Date.now(),
      }),
      "🔒 Emoji and special chars: !@#$%^&*()_+-=[]{}|;:,.<>?",
    ];

    const encryptedResults = [];

    for (let i = 0; i < testCases.length; i++) {
      const sensitiveData = testCases[i];
      console.log(`\n🔐 加密测试用例 ${i + 1}:`);
      console.log(
        `   原始数据: ${sensitiveData.substring(0, 50)}${
          sensitiveData.length > 50 ? "..." : ""
        }`
      );
      console.log(`   数据长度: ${sensitiveData.length} 字符`);

      const encrypted = await crypto.encryptData(sensitiveData);
      encryptedResults.push({ original: sensitiveData, encrypted });

      console.log(`   加密完成: ${encrypted.encrypted.substring(0, 32)}...`);
      console.log(`   加密长度: ${encrypted.encrypted.length} 字符`);
      console.log(`   算法: ${encrypted.algorithm}`);
      console.log(`   时间戳: ${new Date(encrypted.timestamp).toISOString()}`);
    }

    // 🔓 步骤3：解密数据验证
    console.log("\n📋 步骤3：解密数据验证");
    console.log("----------------------------------------");

    let allTestsPassed = true;

    for (let i = 0; i < encryptedResults.length; i++) {
      const { original, encrypted } = encryptedResults[i];

      console.log(`\n🔓 解密测试用例 ${i + 1}:`);

      try {
        const decrypted = await crypto.decryptData(encrypted);
        const isValid = original === decrypted;

        console.log(
          `   解密结果: ${decrypted.substring(0, 50)}${
            decrypted.length > 50 ? "..." : ""
          }`
        );
        console.log(`   验证结果: ${isValid ? "✅ 通过" : "❌ 失败"}`);

        if (!isValid) {
          allTestsPassed = false;
          console.log(`   预期: ${original}`);
          console.log(`   实际: ${decrypted}`);
        }
      } catch (error) {
        allTestsPassed = false;
        console.log(`   ❌ 解密失败: ${error.message}`);
      }
    }

    console.log(
      `\n🎯 总体验证结果: ${
        allTestsPassed ? "✅ 所有测试通过" : "❌ 部分测试失败"
      }`
    );

    // � 步骤3.5：模拟重启后密钥恢复（触发解密数据密钥验证）
    console.log("\n📋 步骤3.5：模拟重启后密钥恢复");
    console.log("----------------------------------------");

    try {
      console.log("🔄 模拟客户端重启...");
      console.log("💾 保存当前状态信息");

      // 保存当前状态
      const currentUserId = crypto.userId;
      const currentAuthToken = await getAuthToken();

      console.log(`📋 当前用户ID: ${currentUserId}`);
      console.log(`🔑 当前Token已保存`);

      // 模拟清理内存中的密钥（但保留存储的加密密钥）
      console.log("🗑️ 清理内存中的明文密钥...");
      crypto.cleanup(false); // 不删除存储的文件

      console.log("🔄 重新初始化客户端（从存储恢复）...");

      // 重新初始化，这次会从存储中恢复密钥，触发解密数据密钥的API调用
      const newCrypto = new ClientSideCrypto("http://localhost:3000");
      await newCrypto.initialize(currentUserId, `Bearer ${currentAuthToken}`);

      console.log("✅ 密钥恢复成功！");
      console.log("🔍 这个过程会触发服务端的二次用户ID验证");
      console.log(`🔑 恢复的密钥指纹: ${newCrypto.getStatus().keyFingerprint}`);

      // 验证恢复的密钥是否能正常工作
      console.log("🧪 验证恢复的密钥...");
      const testData = "这是测试恢复密钥的数据";
      const testEncrypted = await newCrypto.encryptData(testData);
      const testDecrypted = await newCrypto.decryptData(testEncrypted);

      if (testData === testDecrypted) {
        console.log("✅ 恢复的密钥验证通过");
      } else {
        console.log("❌ 恢复的密钥验证失败");
      }

      // 使用恢复的密钥实例继续演示
      crypto = newCrypto;
    } catch (error) {
      console.error("❌ 密钥恢复失败:", error.message);
      console.log("⚠️ 这可能表示用户身份验证失败或存储的密钥无效");
    }

    // �🔄 步骤4：密钥轮换演示
    console.log("\n📋 步骤4：密钥轮换演示");
    console.log("----------------------------------------");

    const oldStatus = crypto.getStatus();
    console.log(`旧密钥指纹: ${oldStatus.keyFingerprint}`);
    console.log(`旧会话ID: ${oldStatus.sessionId}`);

    await crypto.rotateDataKey();

    const newStatus = crypto.getStatus();
    console.log(`新密钥指纹: ${newStatus.keyFingerprint}`);
    console.log(`新会话ID: ${newStatus.sessionId}`);
    console.log(
      `密钥已更换: ${
        oldStatus.keyFingerprint !== newStatus.keyFingerprint
          ? "✅ 是"
          : "❌ 否"
      }`
    );

    // 验证新密钥可以解密旧数据（如果使用相同的主密钥）
    console.log("\n🔄 验证密钥轮换后的兼容性:");
    try {
      const testData = "测试密钥轮换后的加密解密";
      const newEncrypted = await crypto.encryptData(testData);
      const newDecrypted = await crypto.decryptData(newEncrypted);
      console.log(
        `新密钥加密解密: ${testData === newDecrypted ? "✅ 成功" : "❌ 失败"}`
      );
    } catch (error) {
      console.log(`❌ 新密钥测试失败: ${error.message}`);
    }

    // 🏥 步骤5：健康检查
    console.log("\n📋 步骤5：系统健康检查");
    console.log("----------------------------------------");

    try {
      const health = await crypto.healthCheck();
      console.log("客户端状态:");
      console.log(`   状态: ${health.client.status}`);
      console.log(`   已初始化: ${health.client.initialized}`);
      console.log(`   用户ID: ${health.client.userId}`);
      console.log(`   密钥指纹: ${health.client.keyFingerprint}`);

      console.log("服务端状态:");
      console.log(`   状态: ${health.server.status}`);
      if (health.server.service) {
        console.log(`   服务: ${health.server.service}`);
        console.log(`   版本: ${health.server.version}`);
      }
    } catch (error) {
      console.log(`⚠️ 健康检查失败: ${error.message}`);
    }

    // 📊 步骤6：性能测试
    console.log("\n📋 步骤6：性能测试");
    console.log("----------------------------------------");

    const performanceData = "A".repeat(1000); // 1KB数据
    const iterations = 100;

    console.log(`测试数据大小: ${performanceData.length} 字符`);
    console.log(`测试迭代次数: ${iterations}`);

    // 加密性能测试
    const encryptStartTime = Date.now();
    for (let i = 0; i < iterations; i++) {
      await crypto.encryptData(performanceData);
    }
    const encryptEndTime = Date.now();
    const encryptTime = encryptEndTime - encryptStartTime;

    // 解密性能测试
    const testEncrypted = await crypto.encryptData(performanceData);
    const decryptStartTime = Date.now();
    for (let i = 0; i < iterations; i++) {
      await crypto.decryptData(testEncrypted);
    }
    const decryptEndTime = Date.now();
    const decryptTime = decryptEndTime - decryptStartTime;

    console.log(`加密性能: ${(encryptTime / iterations).toFixed(2)} ms/次`);
    console.log(`解密性能: ${(decryptTime / iterations).toFixed(2)} ms/次`);
    console.log(
      `总体性能: ${((encryptTime + decryptTime) / iterations).toFixed(2)} ms/次`
    );

    console.log("\n🎉 演示完成！");
    console.log("=====================================");
    console.log("✅ 所有功能演示成功");
    console.log("🔐 敏感数据始终在客户端加密");
    console.log("🛡️ 服务端无法看到用户数据");
    console.log("🔑 密钥管理安全可靠");
    console.log("=====================================\n");
  } catch (error) {
    console.error("\n❌ 演示过程中发生错误:");
    console.error("=====================================");
    console.error("错误信息:", error.message);
    console.error("错误堆栈:", error.stack);
    console.error("=====================================\n");

    console.log("🔧 故障排除建议:");
    console.log("1. 确保KMS服务端正在运行 (http://localhost:3000)");
    console.log("2. 检查网络连接是否正常");
    console.log("3. 验证AWS KMS配置是否正确");
    console.log("4. 确认认证令牌是否有效");
  } finally {
    // 🗑️ 清理资源
    console.log("🗑️ 清理客户端资源...");
    crypto.cleanup();
    console.log("✅ 资源清理完成\n");
  }
}

// 启动演示
if (require.main === module) {
  console.log("🚀 启动KMS客户端加密演示...\n");

  clientEncryptionDemo().catch((error) => {
    console.error("💥 演示启动失败:", error);
    process.exit(1);
  });
}

module.exports = { clientEncryptionDemo };
