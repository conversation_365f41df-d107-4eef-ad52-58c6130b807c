/**
 * 🧪 纯 AWS Encryption SDK 测试
 * 
 * 验证移除原生加密后的实现
 */

const { CryptoUtils } = require('./src/index');

async function testPureAwsEncryptionSdk() {
  console.log('\n🧪 开始纯 AWS Encryption SDK 测试...\n');

  try {
    // 模拟从服务端获取的数据密钥（32字节）
    const mockDataKey = Buffer.alloc(32);
    for (let i = 0; i < 32; i++) {
      mockDataKey[i] = i;
    }
    const dataKeyBase64 = mockDataKey.toString('base64');
    
    const testUserId = 'test-user-123';
    const testData = '这是一段需要加密的敏感数据 🔐';

    console.log('📝 测试数据:', testData);
    console.log('👤 用户ID:', testUserId);
    console.log('🔑 数据密钥长度:', mockDataKey.length, '字节');
    console.log('');

    // 测试加密
    console.log('🔐 测试 AWS Encryption SDK 加密...');
    const encrypted = await CryptoUtils.encryptData(testData, dataKeyBase64, testUserId);
    
    console.log('✅ 加密成功');
    console.log('📏 加密后长度:', encrypted.encrypted.length);
    console.log('🔧 算法:', encrypted.algorithm);
    console.log('🔍 加密上下文:', encrypted.encryptionContext);
    console.log('');

    // 验证数据格式
    const isValid = CryptoUtils.validateEncryptedData(encrypted);
    console.log('🔍 数据格式验证:', isValid ? '✅ 通过' : '❌ 失败');
    console.log('');

    // 测试解密
    console.log('🔓 测试 AWS Encryption SDK 解密...');
    const decrypted = await CryptoUtils.decryptData(encrypted, dataKeyBase64);
    
    console.log('✅ 解密成功');
    console.log('📝 解密后数据:', decrypted);
    console.log('');

    // 验证数据完整性
    if (decrypted === testData) {
      console.log('✅ 数据完整性验证通过');
    } else {
      console.error('❌ 数据完整性验证失败');
      console.error('原始:', testData);
      console.error('解密:', decrypted);
      return false;
    }

    console.log('\n🎉 纯 AWS Encryption SDK 测试通过！\n');
    
    // 显示最终的实现特点
    console.log('📋 实现特点:');
    console.log('  ✅ 完全使用 AWS Encryption SDK');
    console.log('  ✅ 使用 Raw Keyring，无需 KMS 访问');
    console.log('  ✅ 获得 AWS 官方背书');
    console.log('  ✅ 代码简洁，单一实现');
    console.log('  ✅ 标准信封加密');
    console.log('  ✅ 加密上下文验证');
    console.log('');
    
    return true;

  } catch (error) {
    console.error('\n❌ 测试失败:', error);
    console.error('错误详情:', error.stack);
    return false;
  }
}

// 运行测试
if (require.main === module) {
  testPureAwsEncryptionSdk()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('测试运行失败:', error);
      process.exit(1);
    });
}

module.exports = { testPureAwsEncryptionSdk };
