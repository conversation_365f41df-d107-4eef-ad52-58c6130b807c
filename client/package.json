{"name": "kms-client", "version": "1.0.0", "description": "KMS客户端 - 客户端加密库", "main": "src/index.js", "scripts": {"start": "node src/demo.js", "test": "jest", "dev": "node --inspect src/demo.js", "build": "webpack --mode production", "serve": "webpack serve --mode development"}, "keywords": ["kms", "encryption", "client-side", "security", "crypto"], "author": "xuhong_yao", "license": "MIT", "dependencies": {"node-fetch": "^2.6.7", "crypto-js": "^4.2.0"}, "devDependencies": {"jest": "^29.7.0", "webpack": "^5.89.0", "webpack-cli": "^5.1.4", "webpack-dev-server": "^4.15.1"}, "engines": {"node": ">=14.0.0", "npm": ">=6.0.0"}, "browser": {"crypto": false}}