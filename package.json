{"name": "aws-kms-encryption-demo", "version": "1.0.0", "description": "AWS KMS加密解密演示项目", "main": "start-demo.js", "scripts": {"start": "node start-demo.js", "start:client": "cd client && npm start", "start:server": "cd server && npm start", "test": "cd client && npm test && cd ../server && npm test", "dev": "node --inspect start-demo.js"}, "keywords": ["aws", "kms", "encryption", "decryption", "security"], "author": "xuhong_yao", "license": "MIT", "dependencies": {"dotenv": "^16.3.1"}, "devDependencies": {}, "engines": {"node": ">=14.0.0", "npm": ">=6.0.0"}}