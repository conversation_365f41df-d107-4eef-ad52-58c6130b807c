const express = require('express');
const DataKeyService = require('../services/dataKeyService');
const AuthMiddleware = require('../middleware/auth');

const router = express.Router();
const dataKeyService = new DataKeyService();

/**
 * 🔑 数据密钥管理路由
 * 
 * 安全原则：
 * 1. 所有接口都需要身份验证
 * 2. 用户只能访问自己的数据密钥
 * 3. 服务端不存储明文密钥
 * 4. 支持密钥轮换
 */

/**
 * 🆕 生成新的数据密钥
 * POST /api/generate-data-key
 */
router.post('/generate-data-key', 
  AuthMiddleware.verifyToken,
  AuthMiddleware.validateUserAccess,
  async (req, res) => {
    try {
      const { userId, sessionId } = req.body;
      
      if (!userId || !sessionId) {
        return res.status(400).json({ 
          error: '缺少必需参数: userId 和 sessionId',
          code: 'MISSING_PARAMETERS'
        });
      }

      console.log(`🔑 收到生成数据密钥请求 - 用户: ${userId}, 会话: ${sessionId}`);
      
      const result = await dataKeyService.generateDataKey(userId, sessionId);
      
      res.json({
        success: true,
        message: '数据密钥生成成功',
        ...result
      });

      console.log(`✅ 数据密钥已发送给客户端 - 用户: ${userId}`);
      
    } catch (error) {
      console.error('❌ 生成数据密钥API失败:', error);
      res.status(500).json({ 
        error: '生成数据密钥失败',
        code: 'DATA_KEY_GENERATION_FAILED'
      });
    }
  }
);

/**
 * 🔓 解密已存储的数据密钥
 * POST /api/decrypt-data-key
 */
router.post('/decrypt-data-key',
  AuthMiddleware.verifyToken,
  AuthMiddleware.validateUserAccess,
  async (req, res) => {
    try {
      const { encryptedDataKey, encryptionContext } = req.body;
      const requestUserId = req.user?.userId; // 从JWT中获取用户ID
      
      if (!encryptedDataKey || !encryptionContext) {
        return res.status(400).json({ 
          error: '缺少必需参数: encryptedDataKey 和 encryptionContext',
          code: 'MISSING_PARAMETERS'
        });
      }

      console.log(`🔓 收到解密数据密钥请求 - 用户: ${encryptionContext.userId}, 会话: ${encryptionContext.sessionId}`);
      console.log(`🔍 JWT用户ID: ${requestUserId}`);
      
      // 传递JWT中的用户ID用于密文验证
      const result = await dataKeyService.decryptDataKey(encryptedDataKey, encryptionContext, requestUserId);
      
      res.json({
        success: true,
        message: '数据密钥解密成功',
        ...result
      });

      console.log(`✅ 解密的数据密钥已发送给客户端 - 用户: ${encryptionContext.userId}`);
      
    } catch (error) {
      console.error('❌ 解密数据密钥API失败:', error);
      res.status(500).json({ 
        error: '解密数据密钥失败',
        code: 'DATA_KEY_DECRYPTION_FAILED'
      });
    }
  }
);

/**
 * 🔄 轮换数据密钥
 * POST /api/rotate-data-key
 */
router.post('/rotate-data-key',
  AuthMiddleware.verifyToken,
  AuthMiddleware.validateUserAccess,
  async (req, res) => {
    try {
      const { userId, oldSessionId, newSessionId } = req.body;
      
      if (!userId || !oldSessionId || !newSessionId) {
        return res.status(400).json({ 
          error: '缺少必需参数: userId, oldSessionId 和 newSessionId',
          code: 'MISSING_PARAMETERS'
        });
      }

      console.log(`🔄 收到轮换数据密钥请求 - 用户: ${userId}`);
      
      const result = await dataKeyService.rotateDataKey(userId, oldSessionId, newSessionId);
      
      res.json({
        success: true,
        message: '数据密钥轮换成功',
        ...result
      });

      console.log(`✅ 新数据密钥已发送给客户端 - 用户: ${userId}`);
      
    } catch (error) {
      console.error('❌ 轮换数据密钥API失败:', error);
      res.status(500).json({ 
        error: '轮换数据密钥失败',
        code: 'DATA_KEY_ROTATION_FAILED'
      });
    }
  }
);

/**
 * 🏥 健康检查
 * GET /api/health
 */
router.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    service: 'KMS数据密钥服务',
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  });
});

module.exports = router;
