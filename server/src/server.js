const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const dataKeyRoutes = require('./routes/dataKeyRoutes');
const AuthMiddleware = require('./middleware/auth');
const { port, rateLimitWindowMs, rateLimitMaxRequests, nodeEnv } = require('./config');

/**
 * 🔑 KMS数据密钥服务器
 * 
 * 安全特性：
 * 1. 🛡️ 零信任架构 - 服务端不接触用户敏感数据
 * 2. 🔐  密钥分离 - 主密钥在KMS，数据密钥临时下发
 * 3. 🔒 安全传输 - HTTPS + CORS + 安全头
 * 4. 🚦 速率限制 - 防止暴力攻击
 * 5. 🗑️ 内存清理 - 明文密钥立即清除
 */
class KMSServer {
  constructor() {
    this.app = express();
    this.setupMiddleware();
    this.setupRoutes();
    this.setupErrorHandling();
  }

  setupMiddleware() {
    // 🛡️ 安全头
    this.app.use(helmet({
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          scriptSrc: ["'self'"],
          styleSrc: ["'self'", "'unsafe-inline'"],
          imgSrc: ["'self'", "data:", "https:"],
        },
      },
    }));

    // 🌐 CORS配置
    this.app.use(cors({
      origin: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3001'],
      credentials: true,
      methods: ['GET', 'POST'],
      allowedHeaders: ['Content-Type', 'Authorization']
    }));

    // 🚦 速率限制
    const limiter = rateLimit({
      windowMs: rateLimitWindowMs,
      max: rateLimitMaxRequests,
      message: {
        error: '请求过于频繁，请稍后再试',
        code: 'RATE_LIMIT_EXCEEDED'
      },
      standardHeaders: true,
      legacyHeaders: false,
    });
    this.app.use('/api/', limiter);

    // 📝 请求解析
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

    // 📊 请求日志
    this.app.use((req, res, next) => {
      console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
      next();
    });
  }

  setupRoutes() {
    // � 登录接口（演示用）
    this.app.post('/api/login', (req, res) => {
      try {
        const { username, password } = req.body;
        
        // 演示用的简单验证（实际应用中应该连接数据库）
        if (!username || !password) {
          return res.status(400).json({
            error: '用户名和密码是必需的',
            code: 'MISSING_CREDENTIALS'
          });
        }
        
        // 简单的演示验证（实际应用中应该验证密码哈希）
        const validUsers = {
          'user123': 'password123',
          'admin': 'admin123',
          'demo': 'demo123'
        };
        
        if (validUsers[username] !== password) {
          return res.status(401).json({
            error: '用户名或密码错误',
            code: 'INVALID_CREDENTIALS'
          });
        }
        
        // 生成JWT令牌
        const role = username === 'admin' ? 'admin' : 'user';
        const token = AuthMiddleware.generateToken(username, role);
        
        console.log(`✅ 用户 ${username} 登录成功`);
        
        res.json({
          success: true,
          message: '登录成功',
          token,
          user: {
            userId: username,
            role
          }
        });
        
      } catch (error) {
        console.error('❌ 登录失败:', error);
        res.status(500).json({
          error: '登录服务异常',
          code: 'LOGIN_SERVICE_ERROR'
        });
      }
    });

    // �🔑 数据密钥管理API
    this.app.use('/api', dataKeyRoutes);

    // 🏠 根路径
    this.app.get('/', (req, res) => {
      res.json({
        service: 'KMS数据密钥服务',
        version: '1.0.0',
        status: 'running',
        features: [
          '🔐 数据不离开客户端',
          '🔑 密钥分离架构', 
          '🛡️ 零信任安全',
          '🔄 密钥轮换支持',
          '💾 安全存储'
        ],
        endpoints: {
          health: 'GET /api/health',
          generateDataKey: 'POST /api/generate-data-key',
          decryptDataKey: 'POST /api/decrypt-data-key',
          rotateDataKey: 'POST /api/rotate-data-key'
        }
      });
    });

    // 404处理
    this.app.use('*', (req, res) => {
      res.status(404).json({
        error: '接口不存在',
        code: 'ENDPOINT_NOT_FOUND',
        path: req.originalUrl
      });
    });
  }

  setupErrorHandling() {
    // 全局错误处理
    this.app.use((error, req, res, next) => {
      console.error('❌ 服务器错误:', error);
      
      res.status(500).json({
        error: nodeEnv === 'production' ? '内部服务器错误' : error.message,
        code: 'INTERNAL_SERVER_ERROR',
        timestamp: new Date().toISOString()
      });
    });

    // 优雅关闭
    process.on('SIGTERM', () => {
      console.log('🔄 收到SIGTERM信号，开始优雅关闭...');
      this.gracefulShutdown();
    });

    process.on('SIGINT', () => {
      console.log('🔄 收到SIGINT信号，开始优雅关闭...');
      this.gracefulShutdown();
    });
  }

  start() {
    this.server = this.app.listen(port, () => {
      console.log('\n🚀 KMS数据密钥服务启动成功！');
      console.log('=====================================');
      console.log(`🌐 服务地址: http://localhost:${port}`);
      console.log(`🔧 运行环境: ${nodeEnv}`);
      console.log(`🛡️ 安全特性: 零信任架构`);
      console.log(`🔑 功能: 数据密钥管理`);
      console.log('=====================================\n');
      
      console.log('📋 可用接口:');
      console.log(`  GET  /api/health           - 健康检查`);
      console.log(`  POST /api/generate-data-key - 生成数据密钥`);
      console.log(`  POST /api/decrypt-data-key  - 解密数据密钥`);
      console.log(`  POST /api/rotate-data-key   - 轮换数据密钥`);
      console.log('');
    });
  }

  gracefulShutdown() {
    if (this.server) {
      this.server.close(() => {
        console.log('✅ 服务器已优雅关闭');
        process.exit(0);
      });
    }
  }
}

// 启动服务器
if (require.main === module) {
  try {
    const server = new KMSServer();
    server.start();
  } catch (error) {
    console.error('❌ 服务器启动失败:', error);
    process.exit(1);
  }
}

module.exports = KMSServer;
