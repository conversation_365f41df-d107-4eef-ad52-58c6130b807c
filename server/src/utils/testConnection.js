const { KMSClient, DescribeKeyCommand } = require('@aws-sdk/client-kms');
const { kmsClient, kmsKeyArn } = require('../config');

/**
 * 🔧 测试KMS连接和密钥访问
 */
async function testKMSConnection() {
  console.log('🔧 测试AWS KMS连接...');
  
  try {
    // 测试密钥描述
    const describeKeyCommand = new DescribeKeyCommand({
      KeyId: kmsKeyArn
    });
    
    const response = await kmsClient.send(describeKeyCommand);
    
    console.log('✅ KMS连接成功');
    console.log(`   密钥ID: ${response.KeyMetadata.KeyId}`);
    console.log(`   密钥状态: ${response.KeyMetadata.KeyState}`);
    console.log(`   密钥用途: ${response.KeyMetadata.KeyUsage}`);
    console.log(`   创建时间: ${response.KeyMetadata.CreationDate}`);
    
    if (response.KeyMetadata.KeyState === 'Enabled') {
      console.log('✅ KMS密钥状态正常，可以使用');
      return true;
    } else {
      console.log(`❌ KMS密钥状态异常: ${response.KeyMetadata.KeyState}`);
      return false;
    }
    
  } catch (error) {
    console.error('❌ KMS连接测试失败:', error.message);
    
    if (error.name === 'AccessDeniedException') {
      console.error('💡 提示: 请检查AWS凭证是否有访问该KMS密钥的权限');
    } else if (error.name === 'NotFoundException') {
      console.error('💡 提示: 请检查KMS密钥ARN是否正确');
    } else if (error.name === 'NetworkingError') {
      console.error('💡 提示: 请检查网络连接');
    }
    
    throw error;
  }
}

module.exports = { testKMSConnection };
