const jwt = require('jsonwebtoken');
const { jwtSecret } = require('../config');

/**
 * 🔐 身份验证中间件
 */
class AuthMiddleware {
  
  /**
   * 验证JWT令牌
   */
  static verifyToken(req, res, next) {
    try {
      const authHeader = req.headers.authorization;
      
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return res.status(401).json({ 
          error: '未提供有效的授权令牌',
          code: 'MISSING_TOKEN'
        });
      }

      const token = authHeader.substring(7); // 移除 'Bearer ' 前缀
      
      console.log('🔍 验证JWT令牌...');

      // 严格验证JWT令牌
      try {
        const decoded = jwt.verify(token, jwtSecret);
        console.log('✅ JWT验证成功:', { userId: decoded.userId, role: decoded.role });
        req.user = decoded;
        next();
      } catch (jwtError) {
        console.error('❌ JWT验证失败:', jwtError.message);
        return res.status(401).json({ 
          error: 'JWT令牌验证失败',
          code: 'INVALID_TOKEN',
          details: jwtError.message
        });
      }
      
    } catch (error) {
      console.error('❌ 令牌验证失败:', error);
      return res.status(401).json({ 
        error: '令牌验证失败',
        code: 'TOKEN_VERIFICATION_FAILED'
      });
    }
  }

  /**
   * 验证用户权限
   */
  static validateUserAccess(req, res, next) {
    try {
      // 从不同的位置获取用户ID
      let userId = req.body.userId; // 旧格式（生成密钥）
      if (!userId && req.body.encryptionContext) {
        userId = req.body.encryptionContext.userId; // 新格式（解密密钥）
      }
      
      const requestUserId = req.user?.userId;
      
      if (!userId) {
        return res.status(400).json({ 
          error: '无法确定用户身份',
          code: 'USER_ID_MISSING'
        });
      }
      
      // 确保用户只能访问自己的数据密钥
      if (userId !== requestUserId) {
        console.error(`❌ 用户身份不匹配: 请求用户=${userId}, JWT用户=${requestUserId}`);
        return res.status(403).json({ 
          error: '无权访问其他用户的数据',
          code: 'ACCESS_DENIED'
        });
      }
      
      console.log(`✅ 用户权限验证通过: ${userId}`);
      next();
    } catch (error) {
      console.error('❌ 用户权限验证失败:', error);
      return res.status(403).json({ 
        error: '权限验证失败',
        code: 'PERMISSION_VERIFICATION_FAILED'
      });
    }
  }

  /**
   * 生成JWT令牌（用于测试）
   */
  static generateToken(userId, role = 'user') {
    return jwt.sign(
      { userId, role },
      jwtSecret,
      { expiresIn: '24h' }
    );
  }
}

module.exports = AuthMiddleware;
