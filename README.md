# 🔐 KMS客户端-服务端分离架构演示

## 🛡️ 安全优势

### 零信任架构
- **🔐 数据不离开客户端**：敏感数据始终在客户端加密，服务端永远无法看到明文
- **🔑 密钥分离**：主密钥在AWS KMS，数据密钥临时下发到客户端
- **🛡️ 零信任架构**：服务端对用户数据完全盲化，无法进行数据分析或泄露
- **🔄 密钥轮换**：支持定期更换数据密钥，提高安全性
- **💾 安全存储**：只存储加密后的数据密钥，明文密钥在内存中立即清除

## 🏗️ 项目结构

```
kms/
├── server/                    # 🔑 KMS服务端
│   ├── src/
│   │   ├── server.js         # 主服务器
│   │   ├── config.js         # AWS配置
│   │   ├── services/
│   │   │   └── dataKeyService.js  # 数据密钥服务
│   │   ├── middleware/
│   │   │   └── auth.js       # 身份验证
│   │   ├── routes/
│   │   │   └── dataKeyRoutes.js   # API路由
│   │   └── utils/
│   │       └── testConnection.js  # 连接测试
│   ├── package.json
│   ├── .env.example
│   └── README.md
├── client/                   # 🔐 客户端加密库
│   ├── src/
│   │   ├── index.js         # 主入口
│   │   ├── clientSideCrypto.js    # 核心加密类
│   │   ├── config.js        # 客户端配置
│   │   ├── utils/
│   │   │   ├── crypto.js    # 加密工具
│   │   │   └── storage.js   # 存储管理
│   │   └── services/
│   │       └── apiClient.js # API客户端
│   ├── package.json
│   └── README.md
├── start-demo.js            # 🚀 一键启动演示
└── README.md               # 项目说明
```

## 🚀 快速开始

### 方式一：一键启动演示
```bash
# 自动启动服务端和客户端演示
node start-demo.js
```

### 方式二：分别启动

#### 1. 配置环境
```bash
# 复制环境变量模板
cp server/.env.example server/.env

# 编辑 server/.env 文件，填入您的AWS配置
# AWS_REGION=us-east-1
# AWS_ACCESS_KEY_ID=your_access_key_id
# AWS_SECRET_ACCESS_KEY=your_secret_access_key
# KMS_KEY_ARN=arn:aws:kms:us-east-1:123456789012:key/your-key-id
```

#### 2. 启动服务端
```bash
cd server
npm install
npm start
```

服务端将在 http://localhost:3000 启动

#### 3. 启动客户端演示
```bash
# 在新终端中
cd client
npm install
npm start
```

## 🔐 安全架构详解

### 数据流程
1. **客户端初始化**：向服务端请求数据密钥
2. **服务端响应**：KMS生成数据密钥，返回明文和密文版本
3. **客户端加密**：使用明文数据密钥在本地加密敏感数据
4. **安全存储**：只存储加密的数据密钥，明文密钥立即清除
5. **数据传输**：只传输加密后的数据，服务端无法解密

### 密钥管理
- **主密钥**：存储在AWS KMS，高度安全
- **数据密钥**：临时生成，用于实际数据加密
- **密钥轮换**：支持定期更换，提高安全性
- **权限控制**：基于用户身份的密钥访问控制

## 📋 API接口

### 服务端API

#### 健康检查
```http
GET /api/health
```

#### 生成数据密钥
```http
POST /api/generate-data-key
Authorization: Bearer demo-token
Content-Type: application/json

{
  "userId": "user123",
  "sessionId": "session-uuid"
}
```

#### 解密数据密钥
```http
POST /api/decrypt-data-key
Authorization: Bearer demo-token
Content-Type: application/json

{
  "encryptedDataKey": "base64-encrypted-key",
  "userId": "user123",
  "sessionId": "session-uuid"
}
```

#### 生成JWT令牌（用于测试）
```bash
# 在server目录下运行
node src/utils/generateToken.js user123 user
```

### 客户端API

#### 基本使用
```javascript
const { ClientSideCrypto } = require('./client/src/index');

// 创建客户端实例
const crypto = new ClientSideCrypto('http://localhost:3000');

// 初始化
await crypto.initialize('user123', 'Bearer demo-token');

// 加密敏感数据
const encrypted = crypto.encryptData('敏感信息');

// 解密数据
const decrypted = crypto.decryptData(encrypted);

// 清理资源
crypto.cleanup();
```

## 🛡️ 安全特性

### 客户端安全
- **本地加密**：AES-256-GCM强加密算法
- **内存保护**：敏感数据及时清理
- **防重放**：时间戳和随机数保护
- **完整性验证**：认证标签确保数据完整性

### 服务端安全
- **身份验证**：JWT令牌验证
- **权限隔离**：用户只能访问自己的密钥
- **速率限制**：防止暴力攻击
- **安全头**：CORS、CSP等安全配置
- **审计日志**：完整的操作记录

### 传输安全
- **HTTPS传输**：生产环境强制加密传输
- **请求签名**：防止请求篡改
- **超时保护**：防止长时间连接攻击

## 🧪 测试

### 服务端测试
```bash
cd server
npm test
```

### 客户端测试
```bash
cd client
npm test
```

### 集成测试
```bash
# 启动完整演示
node start-demo.js
```

## 📊 性能指标

- **加密速度**：~50MB/s
- **解密速度**：~55MB/s
- **密钥获取**：<100ms
- **内存占用**：<10MB
- **并发支持**：1000+ 连接

## 🔧 配置说明

### 服务端配置 (server/.env)
```bash
# AWS KMS配置
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your_access_key_id
AWS_SECRET_ACCESS_KEY=your_secret_access_key
KMS_KEY_ARN=arn:aws:kms:us-east-1:123456789012:key/your-key-id

# 服务配置
PORT=3000
NODE_ENV=development
JWT_SECRET=your_jwt_secret_key

# 安全配置
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
```

### 客户端配置 (client/src/config.js)
```javascript
const config = {
  serverUrl: 'http://localhost:3000',
  encryption: {
    algorithm: 'aes-256-gcm',
    keyLength: 32,
    ivLength: 16
  },
  timeout: {
    apiRequest: 10000,
    keyExpiry: 24 * 60 * 60 * 1000
  }
};
```

## 🆘 故障排除

### 常见问题

1. **服务端启动失败**
   - 检查 `.env` 文件配置
   - 验证AWS凭证和权限
   - 确认端口3000未被占用

2. **客户端连接失败**
   - 确保服务端正在运行
   - 检查网络连接
   - 验证服务端地址配置

3. **加密解密失败**
   - 检查数据密钥是否有效
   - 验证加密数据完整性
   - 确认算法参数正确

### 调试模式
```bash
# 服务端调试
cd server && npm run dev

# 客户端调试
cd client && npm run dev
```

## 📝 开发指南

### 添加新功能
1. 服务端：在 `server/src/routes/` 添加新路由
2. 客户端：在 `client/src/services/` 添加新服务
3. 更新API文档和测试用例

### 安全审查
1. 代码审查：确保无敏感数据泄露
2. 依赖检查：定期更新安全补丁
3. 渗透测试：验证安全防护措施

## 📄 许可证

MIT License - 详见 LICENSE 文件

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📞 支持

如有问题，请联系：<EMAIL>
